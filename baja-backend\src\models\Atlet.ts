import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { Atlet as AtletInterface } from '../types';
import User from './User';
import Kontingen from './Kontingen';

interface AtletCreationAttributes extends Optional<AtletInterface, 'id' | 'created_at' | 'updated_at'> {}

class Atlet extends Model<AtletInterface, AtletCreationAttributes> implements AtletInterface {
  public id!: number;
  public nik!: string;
  public name!: string;
  public no_hp?: string;
  public tanggal_lahir!: Date;
  public jenis_kelamin!: 'L' | 'P';
  public agama?: string;
  public alamat?: string;
  public umur!: number;
  public berat_badan!: string;
  public tinggi_badan!: string;
  public status_verifikasi!: 'pending' | 'verified';
  public foto?: string;
  public id_user!: number;
  public id_kontingen!: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

Atlet.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    nik: {
      type: DataTypes.STRING(16),
      allowNull: false,
      unique: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    no_hp: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    tanggal_lahir: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    jenis_kelamin: {
      type: DataTypes.ENUM('L', 'P'),
      allowNull: false,
    },
    agama: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    alamat: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    umur: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    berat_badan: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    tinggi_badan: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status_verifikasi: {
      type: DataTypes.ENUM('pending', 'verified'),
      allowNull: false,
      defaultValue: 'pending',
    },
    foto: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    id_user: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
    },
    id_kontingen: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Kontingen,
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'atlet',
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  }
);

export default Atlet;
