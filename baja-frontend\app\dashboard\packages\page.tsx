'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import CardSkeleton from '@/components/ui/CardSkeleton';
import PackageModal from '@/components/modals/PackageModal';
import DeleteConfirmModal from '@/components/modals/DeleteConfirmModal';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CubeIcon,
  PhotoIcon,
} from '@heroicons/react/24/outline';
import { adminService } from '@/lib/admin';
import { AdminPackage, AdminPackagesResponse } from '@/types';
import toast from 'react-hot-toast';

const PackagesPage = () => {
  const { user } = useAuth();
  const [packages, setPackages] = useState<AdminPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Modal states
  const [showPackageModal, setShowPackageModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<AdminPackage | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [deleteLoading, setDeleteLoading] = useState(false);

  const fetchPackages = async () => {
    try {
      setLoading(true);
      const response = await adminService.getPackages({
        page: currentPage,
        limit: 12,
        search: searchTerm
      });
      setPackages(response.packages);
      setTotalPages(response.pagination.totalPages);
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch packages');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackages();
  }, [currentPage, searchTerm]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchPackages();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // CRUD Handlers
  const handleCreatePackage = () => {
    setSelectedPackage(null);
    setModalMode('create');
    setShowPackageModal(true);
  };

  const handleEditPackage = (pkg: AdminPackage) => {
    setSelectedPackage(pkg);
    setModalMode('edit');
    setShowPackageModal(true);
  };

  const handleDeletePackage = (pkg: AdminPackage) => {
    setSelectedPackage(pkg);
    setShowDeleteModal(true);
  };

  const handleSavePackage = async (packageData: Partial<AdminPackage>) => {
    let savedPackage;
    if (modalMode === 'create') {
      savedPackage = await adminService.createPackage(packageData);
    } else if (selectedPackage) {
      savedPackage = await adminService.updatePackage(selectedPackage.id, packageData);
    }
    fetchPackages(); // Refresh the list
    return savedPackage;
  };

  const handleConfirmDelete = async () => {
    if (!selectedPackage) return;

    setDeleteLoading(true);
    try {
      await adminService.deletePackage(selectedPackage.id);
      toast.success('Package deleted successfully!');
      setShowDeleteModal(false);
      setSelectedPackage(null);
      fetchPackages(); // Refresh the list
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete package');
    } finally {
      setDeleteLoading(false);
    }
  };

  if (user?.role !== 'admin') {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
          <p className="text-gray-600 mt-2">You don't have permission to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Package Management</h1>
            <p className="text-gray-600 mt-1">Manage service packages and offerings</p>
          </div>
          <Button
            className="flex items-center space-x-2"
            onClick={handleCreatePackage}
          >
            <PlusIcon className="h-4 w-4" />
            <span>Add Package</span>
          </Button>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-6">
            <form onSubmit={handleSearch} className="flex gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search packages by name or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <Button type="submit" className="flex items-center space-x-2">
                <MagnifyingGlassIcon className="h-4 w-4" />
                <span>Search</span>
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Packages Grid */}
        {loading ? (
          <CardSkeleton
            count={8}
            hasImage={true}
            gridCols="grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
          />
        ) : packages.length === 0 ? (
          <div className="text-center py-12">
            <CubeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No packages found</p>
            <p className="text-gray-500 text-sm mt-1">Try adjusting your search criteria</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {packages.map((pkg) => (
              <Card key={pkg.id} className="hover:shadow-lg transition-shadow overflow-hidden">
                {/* Package Image */}
                <div className="h-48 bg-gray-100 relative">
                  {pkg.images ? (
                    <img
                      src={pkg.images}
                      alt={pkg.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder-image.jpg';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <PhotoIcon className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                  
                  {/* Actions Overlay */}
                  <div className="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button variant="outline" size="sm" className="bg-white/90 hover:bg-white">
                      <EyeIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-white/90 hover:bg-white"
                      onClick={() => handleEditPackage(pkg)}
                    >
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-white/90 hover:bg-white text-red-600 hover:text-red-700"
                      onClick={() => handleDeletePackage(pkg)}
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <CardContent className="p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 truncate">
                    {pkg.name}
                  </h3>

                  <p className="text-gray-600 text-sm mb-4 overflow-hidden" style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical'
                  }}>
                    {pkg.description || 'No description available'}
                  </p>

                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span>Created: {formatDate(pkg.created_at)}</span>
                    <div className="flex space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditPackage(pkg)}
                      >
                        <PencilIcon className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeletePackage(pkg)}
                      >
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Modals */}
        <PackageModal
          isOpen={showPackageModal}
          onClose={() => setShowPackageModal(false)}
          onSave={handleSavePackage}
          package={selectedPackage}
          mode={modalMode}
        />

        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleConfirmDelete}
          title="Delete Package"
          message="Are you sure you want to delete this package? This action cannot be undone."
          itemName={selectedPackage?.name}
          loading={deleteLoading}
        />
      </div>
    </DashboardLayout>
  );
};

export default PackagesPage;
