'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import CardSkeleton from '@/components/ui/CardSkeleton';
import ImageUpload from '@/components/ui/ImageUpload';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  PhotoIcon,
} from '@heroicons/react/24/outline';
import { adminService } from '@/lib/admin';
import { uploadService } from '@/lib/upload.service';
import { AdminGallery, AdminGalleryResponse } from '@/types';
import toast from 'react-hot-toast';

const GalleryPage = () => {
  const { user } = useAuth();
  const [gallery, setGallery] = useState<AdminGallery[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadDescription, setUploadDescription] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const fetchGallery = async () => {
    try {
      setLoading(true);
      const response = await adminService.getGallery({
        page: currentPage,
        limit: 12,
        search: searchTerm
      });
      setGallery(response.gallery);
      setTotalPages(response.pagination.totalPages);
    } catch (error: any) {
      toast.error(error.message || 'Failed to fetch gallery');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGallery();
  }, [currentPage, searchTerm]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchGallery();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleImageSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleImageUpload = async (file: File) => {
    if (!uploadDescription.trim()) {
      toast.error('Please enter a description for the image');
      return;
    }

    try {
      await uploadService.uploadGalleryImage(file, uploadDescription);
      toast.success('Image uploaded successfully!');
      setShowUploadModal(false);
      setUploadDescription('');
      setSelectedFile(null);
      fetchGallery(); // Refresh gallery
    } catch (error: any) {
      toast.error(error.message || 'Failed to upload image');
    }
  };

  const handleDeleteImage = async (id: string) => {
    if (!confirm('Are you sure you want to delete this image?')) return;

    try {
      await adminService.deleteGalleryItem(id);
      toast.success('Image deleted successfully!');
      fetchGallery(); // Refresh gallery
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete image');
    }
  };

  if (user?.role !== 'admin') {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
          <p className="text-gray-600 mt-2">You don't have permission to access this page.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gallery Management</h1>
            <p className="text-gray-600 mt-1">Manage gallery images and media content</p>
          </div>
          <Button
            className="flex items-center space-x-2"
            onClick={() => setShowUploadModal(true)}
          >
            <PlusIcon className="h-4 w-4" />
            <span>Add Image</span>
          </Button>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-6">
            <form onSubmit={handleSearch} className="flex gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search gallery by description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <Button type="submit" className="flex items-center space-x-2">
                <MagnifyingGlassIcon className="h-4 w-4" />
                <span>Search</span>
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Gallery Grid */}
        {loading ? (
          <CardSkeleton
            count={10}
            hasImage={true}
            gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
          />
        ) : gallery.length === 0 ? (
          <div className="text-center py-12">
            <PhotoIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No gallery images found</p>
            <p className="text-gray-500 text-sm mt-1">Try adjusting your search criteria</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {gallery.map((item) => (
              <Card key={item.id} className="group hover:shadow-lg transition-shadow overflow-hidden">
                {/* Image */}
                <div className="aspect-square bg-gray-100 relative overflow-hidden">
                  {item.images ? (
                    <img
                      src={uploadService.getThumbnailUrl(item.images)}
                      alt={item.description || 'Gallery image'}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder-image.jpg';
                      }}
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <PhotoIcon className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                  
                  {/* Actions Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                    <Button variant="outline" size="sm" className="bg-white/90 hover:bg-white">
                      <EyeIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="bg-white/90 hover:bg-white">
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-white/90 hover:bg-white text-red-600 hover:text-red-700"
                      onClick={() => handleDeleteImage(item.id)}
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <CardContent className="p-3">
                  <p className="text-sm text-gray-900 mb-1 overflow-hidden" style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}>
                    {item.description || 'No description'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatDate(item.created_at)}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Upload Modal */}
        {showUploadModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold mb-4">Upload New Image</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <Input
                    type="text"
                    placeholder="Enter image description..."
                    value={uploadDescription}
                    onChange={(e) => setUploadDescription(e.target.value)}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Image
                  </label>
                  <ImageUpload
                    onImageSelect={handleImageSelect}
                    onImageUpload={handleImageUpload}
                    placeholder="Select an image to upload"
                    maxSize={5}
                    acceptedFormats={['image/jpeg', 'image/jpg', 'image/png']}
                    showPreview={true}
                    uploadButtonText="Upload to Gallery"
                    selectButtonText="Choose Image"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowUploadModal(false);
                    setUploadDescription('');
                    setSelectedFile(null);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default GalleryPage;
