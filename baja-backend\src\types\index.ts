export interface User {
  id: number;
  profile?: string;
  name: string;
  email: string;
  no_hp?: string;
  alamat?: string;
  agama?: string;
  password: string;
  role: 'admin' | 'admin-event' | 'ketua-kontingen';
  status: 0 | 1;
  created_at: Date;
  updated_at: Date;
}

export interface Event {
  id: number;
  name: string;
  description?: string;
  start_date: Date;
  end_date: Date;
  lokasi: string;
  biaya_registrasi: number;
  metode_pembayaran: string;
  status: 'active' | 'completed';
  event_image?: string;
  event_proposal?: string;
  event_pemenang?: string;
  id_user: number;
  created_at: Date;
  updated_at: Date;
}

export interface Atlet {
  id: number;
  nik: string;
  name: string;
  no_hp?: string;
  tanggal_lahir: Date;
  jenis_kelamin: 'L' | 'P';
  agama?: string;
  alamat?: string;
  umur: number;
  berat_badan: string;
  tinggi_badan: string;
  status_verifikasi: 'pending' | 'verified';
  foto?: string;
  id_user: number;
  id_kontingen: number;
  created_at: Date;
  updated_at: Date;
}

export interface Kontingen {
  id: number;
  name: string;
  negara: string;
  provinsi: string;
  kabupaten_kota: string;
  id_user: number;
  created_at: Date;
  updated_at: Date;
}

export interface Official {
  id: number;
  profile?: string;
  name: string;
  no_hp: string;
  alamat: string;
  agama: string;
  jenis_kelamin: 'M' | 'F';
  id_kontingen: number;
  created_at: Date;
  updated_at: Date;
}

export interface Paket {
  id: number;
  name: string;
  images?: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Gallery {
  id: number;
  images: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

export interface PendaftaranEvent {
  id: number;
  id_event: number;
  id_kontingen: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: Date;
  updated_at: Date;
}

export interface AuthRequest extends Request {
  user?: User;
}

export interface JWTPayload {
  id: number;
  email: string;
  role: string;
  event_id?: number | undefined;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface DashboardStats {
  count_event: number;
  count_kontingen: number;
  count_atlet: number;
  count_official: number;
}
