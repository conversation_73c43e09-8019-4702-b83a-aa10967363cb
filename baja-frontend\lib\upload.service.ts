import { api } from './api';

export interface UploadResponse {
  success: boolean;
  message: string;
  data: {
    filename?: string;
    url?: string;
    optimizedUrl?: string;
    thumbnailUrl?: string;
  };
}

class UploadService {
  // Gallery upload
  async uploadGalleryImage(file: File, description: string): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('description', description);

    const response = await api.post('/gallery', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  // Event image upload
  async uploadEventImage(eventId: string, file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileType', 'gambar');

    const response = await api.post(`/events/${eventId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  // Event proposal upload
  async uploadEventProposal(eventId: string, file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileType', 'proposal');

    const response = await api.post(`/events/${eventId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  // Atlet photo upload
  async uploadAtletPhoto(atletId: string, file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('photo', file);

    const response = await api.post(`/atlet/${atletId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  // Package image upload (if needed)
  async uploadPackageImage(packageId: string, file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('image', file);

    const response = await api.post(`/paket/${packageId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  // Profile photo upload
  async uploadProfilePhoto(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('photo', file);

    const response = await api.post('/auth/upload-photo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  // Generic file upload with progress
  async uploadWithProgress(
    url: string,
    file: File,
    fieldName: string = 'file',
    additionalData?: Record<string, string>,
    onProgress?: (progress: number) => void
  ): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append(fieldName, file);

    // Add additional form data
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    const response = await api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  }

  // Validate image file
  validateImageFile(file: File, maxSizeMB: number = 5): { isValid: boolean; error?: string } {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Please select a valid image file (JPEG, JPG, or PNG)'
      };
    }

    if (file.size > maxSizeMB * 1024 * 1024) {
      return {
        isValid: false,
        error: `File size must be less than ${maxSizeMB}MB`
      };
    }

    return { isValid: true };
  }

  // Validate document file
  validateDocumentFile(file: File, maxSizeMB: number = 10): { isValid: boolean; error?: string } {
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Please select a valid document file (PDF, DOC, or DOCX)'
      };
    }

    if (file.size > maxSizeMB * 1024 * 1024) {
      return {
        isValid: false,
        error: `File size must be less than ${maxSizeMB}MB`
      };
    }

    return { isValid: true };
  }

  // Get optimized image URL
  getOptimizedImageUrl(originalUrl: string, width?: number, height?: number): string {
    if (!originalUrl.includes('cloudinary.com')) {
      return originalUrl;
    }

    // Extract public_id from Cloudinary URL
    const urlParts = originalUrl.split('/');
    const uploadIndex = urlParts.findIndex(part => part === 'upload');
    
    if (uploadIndex === -1) return originalUrl;

    // Insert transformation parameters
    const transformations = [];
    if (width) transformations.push(`w_${width}`);
    if (height) transformations.push(`h_${height}`);
    transformations.push('c_limit', 'f_auto', 'q_auto');

    const transformationString = transformations.join(',');
    urlParts.splice(uploadIndex + 1, 0, transformationString);

    return urlParts.join('/');
  }

  // Get thumbnail URL
  getThumbnailUrl(originalUrl: string): string {
    return this.getOptimizedImageUrl(originalUrl, 300, 200);
  }
}

export const uploadService = new UploadService();
