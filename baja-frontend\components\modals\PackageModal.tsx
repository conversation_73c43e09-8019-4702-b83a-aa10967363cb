'use client';

import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import ImageUpload from '@/components/ui/ImageUpload';
import { AdminPackage } from '@/types';
import { uploadService } from '@/lib/upload.service';
import toast from 'react-hot-toast';

interface PackageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (packageData: Partial<AdminPackage>) => Promise<void>;
  package?: AdminPackage | null;
  mode: 'create' | 'edit';
}

const PackageModal: React.FC<PackageModalProps> = ({
  isOpen,
  onClose,
  onSave,
  package: pkg,
  mode
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    images: ''
  });
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);

  useEffect(() => {
    if (mode === 'edit' && pkg) {
      setFormData({
        name: pkg.name || '',
        description: pkg.description || '',
        images: pkg.images || ''
      });
    } else {
      setFormData({
        name: '',
        description: '',
        images: ''
      });
    }
    setSelectedFile(null);
  }, [mode, pkg, isOpen]);

  const handleImageSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleImageUpload = async (file: File): Promise<void> => {
    // Just store the file for now, we'll upload it after creating the package
    setSelectedFile(file);
    toast.success('Image selected! It will be uploaded when you save the package.');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Please fill in the package name');
      return;
    }

    setLoading(true);
    try {
      const savedPackage = await onSave(formData);

      // Upload image if selected
      if (selectedFile && savedPackage?.id) {
        setUploadingImage(true);
        try {
          await uploadService.uploadPackageImage(savedPackage.id.toString(), selectedFile);
          toast.success('Package and image uploaded successfully!');
        } catch (uploadError: any) {
          toast.error('Package saved but image upload failed: ' + uploadError.message);
        } finally {
          setUploadingImage(false);
        }
      } else {
        toast.success(`Package ${mode === 'create' ? 'created' : 'updated'} successfully!`);
      }

      onClose();
    } catch (error: any) {
      toast.error(error.message || `Failed to ${mode} package`);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === 'create' ? 'Create New Package' : 'Edit Package'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Package Name *
            </label>
            <Input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter package name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter package description"
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Package Image
            </label>
            <ImageUpload
              onImageSelect={handleImageSelect}
              onImageUpload={handleImageUpload}
              currentImageUrl={formData.images}
              placeholder="Select a package image"
              maxSize={5}
              acceptedFormats={['image/jpeg', 'image/jpg', 'image/png']}
              showPreview={true}
              uploadButtonText="Upload Image"
              selectButtonText="Choose Image"
              disabled={uploadingImage}
            />
            {formData.images && (
              <p className="text-sm text-green-600 mt-1">
                ✓ Image uploaded successfully
              </p>
            )}
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              {loading ? 'Saving...' : (mode === 'create' ? 'Create Package' : 'Update Package')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PackageModal;
