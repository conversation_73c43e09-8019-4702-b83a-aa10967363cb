'use client';

import React, { useState, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { Paket } from '@/types';
import { formatCurrency } from '@/lib/utils';
import { CheckCircleIcon } from '@heroicons/react/24/outline';
import api from '@/lib/api';

const PackagesPage = () => {
  const [packages, setPackages] = useState<Paket[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await api.get('/paket/active');
        if (response.data.success && response.data.data) {
          setPackages(response.data.data);
        } else {
          console.error('Failed to fetch packages:', response.data.message);
          setPackages([]);
        }
      } catch (error) {
        console.error('Error fetching packages:', error);
        setPackages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, []);

  const getDefaultFeatures = (): string[] => {
    return [
      'Manajemen Event',
      'Registrasi Peserta',
      'Laporan Event',
      'Support 24/7'
    ];
  };

  const getPopularPackage = () => {
    // Mark the middle package as popular
    return packages.length > 1 ? packages[1].id : null;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="pt-16">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
            <h1 className="text-4xl font-bold text-gray-900">Paket Berlangganan</h1>
            <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
              Pilih paket yang sesuai dengan kebutuhan event Anda. 
              Semua paket dilengkapi dengan fitur-fitur terbaik untuk mengelola event olahraga bela diri.
            </p>
          </div>
        </div>

        {/* Packages */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[...Array(3)].map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <CardHeader className="text-center">
                    <div className="h-6 bg-gray-200 rounded mb-4"></div>
                    <div className="h-8 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[...Array(6)].map((_, i) => (
                        <div key={i} className="h-4 bg-gray-200 rounded"></div>
                      ))}
                    </div>
                    <div className="mt-6 h-10 bg-gray-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : packages.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="mt-2 text-sm font-medium text-gray-900">Tidak ada paket</h3>
              <p className="mt-1 text-sm text-gray-500">
                Belum ada paket yang tersedia saat ini.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {packages.map((pkg) => {
                const isPopular = pkg.id === getPopularPackage();
                const features = getDefaultFeatures();

                return (
                  <Card
                    key={pkg.id}
                    className={`relative ${isPopular ? 'ring-2 ring-primary-500 shadow-lg scale-105' : ''}`}
                  >
                    {isPopular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                          Paling Populer
                        </span>
                      </div>
                    )}

                    <CardHeader className="text-center">
                      <div className="mb-4">
                        <img
                          src={pkg.images || '/placeholder-package.jpg'}
                          alt={pkg.name}
                          className="w-full h-32 object-cover rounded-lg"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder-package.jpg';
                          }}
                        />
                      </div>
                      <CardTitle className="text-2xl font-bold">{pkg.name}</CardTitle>
                      <div className="text-lg text-primary-600 mt-2">
                        Paket Premium
                      </div>
                      {pkg.description && (
                        <p className="text-sm text-gray-600 mt-2">{pkg.description}</p>
                      )}
                    </CardHeader>

                    <CardContent>
                      <ul className="space-y-3 mb-6">
                        {features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-600">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      <Button
                        className="w-full"
                        variant={isPopular ? 'primary' : 'outline'}
                        size="lg"
                      >
                        Pilih Paket
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>

        {/* FAQ Section */}
        <div className="bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900">Pertanyaan Umum</h2>
              <p className="mt-4 text-gray-600">
                Jawaban untuk pertanyaan yang sering diajukan tentang paket berlangganan
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Apakah bisa upgrade paket di tengah event?
                </h3>
                <p className="text-gray-600">
                  Ya, Anda dapat melakukan upgrade paket kapan saja. Perbedaan harga akan dihitung secara proporsional.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Bagaimana sistem pembayaran?
                </h3>
                <p className="text-gray-600">
                  Pembayaran dapat dilakukan melalui transfer bank, e-wallet, atau kartu kredit. Pembayaran dilakukan per event.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Apakah ada trial gratis?
                </h3>
                <p className="text-gray-600">
                  Ya, kami menyediakan trial gratis selama 7 hari untuk semua paket. Tidak ada biaya tersembunyi.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Bagaimana dengan support teknis?
                </h3>
                <p className="text-gray-600">
                  Semua paket mendapat support teknis. Paket Professional dan Enterprise mendapat prioritas lebih tinggi.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-primary-600">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Siap Memulai Event Anda?
            </h2>
            <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
              Bergabunglah dengan ribuan event organizer yang telah mempercayai platform kami
            </p>
            <Button size="lg" variant="secondary">
              Mulai Trial Gratis
            </Button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default PackagesPage;
