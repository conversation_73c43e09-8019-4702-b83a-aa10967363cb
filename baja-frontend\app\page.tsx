'use client';

import React from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import Button from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Link from 'next/link';
import {
  TrophyIcon,
  UsersIcon,
  CalendarDaysIcon,
  StarIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const HomePage = () => {
  const features = [
    {
      icon: TrophyIcon,
      title: 'Event Management',
      description: 'Kelola event olahraga bela diri dengan mudah dan efisien'
    },
    {
      icon: UsersIcon,
      title: 'Manajemen Atlet',
      description: 'Daftarkan dan kelola data atlet serta kontingen'
    },
    {
      icon: CalendarDaysIcon,
      title: 'Penjadwalan',
      description: 'Atur jadwal pertandingan dan acara dengan sistematis'
    },
    {
      icon: StarIcon,
      title: 'Sistem Penilaian',
      description: 'Catat hasil pertandingan dan tentukan pemenang'
    }
  ];

  const packages = [
    {
      name: 'Basic',
      price: 'Rp 500.000',
      features: [
        'Manajemen Event Dasar',
        'Registrasi Atlet',
        '<PERSON>por<PERSON>',
        'Support Email'
      ]
    },
    {
      name: 'Professional',
      price: 'Rp 1.000.000',
      features: [
        'Semua fitur Basic',
        'Manajemen Kontingen',
        'Sistem Penilaian',
        'Laporan Detail',
        'Support Prioritas'
      ],
      popular: true
    },
    {
      name: 'Enterprise',
      price: 'Rp 2.000.000',
      features: [
        'Semua fitur Professional',
        'Multi Event',
        'Custom Branding',
        'API Access',
        'Dedicated Support'
      ]
    }
  ];

  const stats = [
    { label: 'Event Terselenggara', value: '150+' },
    { label: 'Atlet Terdaftar', value: '5000+' },
    { label: 'Kontingen Aktif', value: '200+' },
    { label: 'Kepuasan Pengguna', value: '98%' }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="pt-16 bg-gradient-to-br from-primary-50 to-primary-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              BAJA Event Organizer
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Platform terpercaya untuk mengelola event olahraga bela diri.
              Sistem informasi yang cerdas dan mudah digunakan untuk semua kebutuhan event organizer Anda.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register">
                <Button size="lg" className="w-full sm:w-auto">
                  Mulai Sekarang
                </Button>
              </Link>
              <Link href="/events">
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  Lihat Event
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Fitur Unggulan
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Dapatkan semua yang Anda butuhkan untuk mengelola event olahraga bela diri
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <feature.icon className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Paket Berlangganan
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Pilih paket yang sesuai dengan kebutuhan event Anda
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <Card
                key={index}
                className={`relative ${pkg.popular ? 'ring-2 ring-primary-500 shadow-lg' : ''}`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Paling Populer
                    </span>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary-600 mt-4">
                    {pkg.price}
                  </div>
                  <div className="text-gray-500">per event</div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className="w-full mt-6"
                    variant={pkg.popular ? 'primary' : 'outline'}
                  >
                    Pilih Paket
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Siap Memulai Event Anda?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Bergabunglah dengan ribuan event organizer yang telah mempercayai platform kami
          </p>
          <Link href="/auth/register">
            <Button size="lg" variant="secondary">
              Daftar Sekarang
            </Button>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default HomePage;
